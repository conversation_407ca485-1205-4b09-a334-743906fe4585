package com.cet.eem.alarmstrategyservice.controller;

import com.cet.datacenter.annotation.OperationLog;
import com.cet.datacenter.annotation.RequiredPermission;
import com.cet.eem.bll.alarmstrategy.exception.APIException;
import com.cet.datacenter.base.Result;
import com.cet.datacenter.bzenum.language.AlarmInterEnum;
import com.cet.datacenter.util.BeanMapper;
import com.cet.eem.bll.alarmstrategy.model.AlgorithmBO;
import com.cet.eem.bll.alarmstrategy.model.vo.AlgorithmVO;
import com.cet.eem.bll.alarmstrategy.model.vo.DetailSearchVo;
import com.cet.eem.bll.alarmstrategy.model.vo.JudgeVO;
import com.cet.eem.bll.alarmstrategy.service.IAlgorithmManagerService;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.event.model.algorithm.pecservice.AlarmAlgorithmDataVo;
import com.cet.futureblue.i18n.LanguageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 告警配置-告警算法库页面相关接口
 * @Author: liuhaojian
 * @Date: Created in 10:55 2021/06/22
 */
@Api(value = "eem/v1/alarm-algorithm", tags = "告警算法库接口")
@RequestMapping(value = "eem/v1/alarm-algorithm")
@RestController
@Slf4j
public class AlarmAlgorithmController {

    @Autowired
    IAlgorithmManagerService algorithmManagerService;

    @ApiOperation(value = "添加或修改算法信息")
    @OperationLog(operationtype = 1, subtype = 7, description = "info.common.log52")
    @PostMapping(value = "/update/algorithm-info")
    @RequiredPermission(enabled = false)
    public Result<Boolean> algorithmInfo(@RequestBody @ApiParam(name = "algorithmVo", value = "添加一个规则信息或修改一个指定的算法信息", required = true) AlgorithmVO algorithmVo) throws Exception {
        AlgorithmBO algorithmBO = BeanMapper.map(algorithmVo, AlgorithmBO.class);
        algorithmBO.setProjectId(GlobalInfoUtils.getProjectId());
        Result<Boolean> result = algorithmManagerService.algorithmInfo(algorithmBO);
        return result;
    }


    @ApiOperation(value = "查看算法详情")
    @PostMapping(value = "/algorithm-detail")
    @RequiredPermission(enabled = false)
    public Result<AlarmAlgorithmDataVo> algorithmDetail(@RequestBody @ApiParam(name = "detailSearchVo", value = "获取算法详情", required = true) DetailSearchVo detailSearchVo) throws Exception {
        if (detailSearchVo.getRuleID() == null || detailSearchVo.getRuleID() <= 0) {
            throw new APIException(LanguageUtil.getMessage(AlarmInterEnum.ERROR_ALGORITHMMANAGERSERVICE_ALGORITHMDETAIL_ALGORITHM.getMsg()));
        }
        detailSearchVo.setRuleType(detailSearchVo.getRuleType() == 4 ? 101L : 102L);
        Result<AlarmAlgorithmDataVo> result = algorithmManagerService.algorithmDetail(detailSearchVo);
        return result;
    }

    @ApiOperation(value = "判断是否能新建算法")
    @OperationLog(operationtype = 1, subtype = 7, description = "info.common.log53")
    @GetMapping(value = "/judge/algorithm-create")
    @RequiredPermission(enabled = false)
    public Result<JudgeVO> canCreateAlgorithm() {
        JudgeVO result = algorithmManagerService.canCreateAlgorithm();
        Result<JudgeVO> res = new Result<>();
        res.setData(result);
        return res;
    }
}
