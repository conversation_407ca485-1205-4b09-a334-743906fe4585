package com.cet.eem.alarmstrategyservice.controller;

import com.cet.datacenter.annotation.OperationLog;
import com.cet.datacenter.annotation.RequiredPermission;
import com.cet.datacenter.util.BeanMapper;
import com.cet.datacenter.util.PushNoticeUtil;
import com.cet.datacenter.util.ReportUtils;
import com.cet.eem.bll.alarmstrategy.constant.AlarmEventWebPushLogType;
import com.cet.datacenter.base.BaseController;
import com.cet.datacenter.base.BaseVO;
import com.cet.datacenter.base.Result;
import com.cet.datacenter.bzenum.language.CommonInterEnum;
import com.cet.datacenter.constant.BaseRTC;
import com.cet.datacenter.constant.Constants;
import com.cet.eem.bll.alarmstrategy.constant.CommonCacheKeyConstant;
import com.cet.eem.bll.alarmstrategy.constant.CommonConstant;
import com.cet.eem.bll.alarmstrategy.handle.AlarmEventWebPushHandler;
import com.cet.eem.bll.alarmstrategy.model.*;
import com.cet.eem.bll.alarmstrategy.model.vo.AlarmCauseClassifyVO;
import com.cet.eem.bll.alarmstrategy.model.vo.CustomAlarmLevelVO;
import com.cet.eem.bll.alarmstrategy.model.vo.TabSearchVO;
import com.cet.eem.bll.alarmstrategy.service.*;
import com.cet.eem.bll.alarmstrategy.util.RedisLockUtil;
import com.cet.eem.bll.common.def.HttpRequestType;
import com.cet.eem.bll.common.model.info.ProcessInfo;
import com.cet.eem.bll.common.model.info.ProcessInfoExtend;
import com.cet.eem.bll.common.model.info.ProcessInfoWithType;
import com.cet.eem.bll.common.model.info.def.EnumImportType;
import com.cet.eem.bll.common.model.info.def.ImportRatioStatusDef;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.futureblue.i18n.LanguageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;


/**
 * @ClassName: AlarmManagerController
 * @Description: 告警管理接口
 * @Author: kyn
 * @Date: 2021/04/29 14:20
 **/
@Api(value = "eem/v1/alarmManage", tags = "告警管理接口")
@RequestMapping(value = "eem/v1/alarmManage")
@RestController
@Slf4j
public class AlarmManagerController extends BaseController {

    @Autowired
    private IAlarmManageService alarmManageService;
    @Autowired
    private IAlarmExportService alarmExportService;
//    @Autowired
//    private IAlarmLightService alarmLightService;
    @Autowired
    private IAlarmSettingService alarmSettingService;
    @Autowired
    IAlarmRingService ringService;
    @Resource(name = "commonExecutor")
    Executor executor;
    @Autowired
    RedisLockUtil redisLockUtil;
    @Value("${folder.path:'/home/<USER>'}")
    private String attachmentFilePath;
    @Autowired
    INoticeService noticeService;

    private static final int CUSTOM_EXPORT_TYPE = 203;
    private static final int REAL_TIME_ALARM_EXPORT_TYPE = 1;
    private static final int REPAIR_ALARM_EXPORT_TYPE = 3;

    @ApiOperation(value = "告警事件列表查询")
    @RequiredPermission(enabled = false)
    @PostMapping(value = "/query/alarm-event-log")
    public BaseVO<List<EventLogBO>> queryAlarmEventLog(@RequestBody @ApiParam(name = "searchVO", value = "查询条件", required = true) TabSearchVO searchVO) throws Exception {
        TabSearchBO searchBO = BeanMapper.map(searchVO, TabSearchBO.class);
        searchBO.setNoAssembleFlag(false);
        Long userId = GlobalInfoUtils.getUserId();
        Long projectId = GlobalInfoUtils.getProjectId();
        Result<List<EventLogBO>> list = alarmManageService.queryAlarmEventList(searchBO, userId, projectId);
        return new BaseVO(list.getData(), list.getTotal());
    }

    @ApiOperation(value = "个人工作台与消息中心查询告警事件")
    @RequiredPermission(enabled = false)
    @PostMapping(value = "/query/alarm-event-logs")
    public BaseVO queryAlarmEventLogs(@RequestBody @ApiParam(name = "searchVO", value = "查询条件", required = true) TabSearchVO searchVO) throws Exception {
        TabSearchBO searchBO = BeanMapper.map(searchVO, TabSearchBO.class);
        Result<List<EventLogBO>> list = alarmManageService.queryAlarmRelatimeEventList(searchBO);
        return new BaseVO(list.getData(), list.getTotal());
    }

    @ApiOperation(value = "获取节点树")
    @RequiredPermission(enabled = false)
    @GetMapping("/query-data")
    public List queryData(HttpServletRequest request) throws Exception {
        Long userId;
        String header = request.getHeader("User-ID");
        if (StringUtils.isBlank(header)) {
            userId = CommonConstant.ROOT_ID;
        } else {
            userId = Long.valueOf(request.getHeader("User-ID"));
        }
        List list = alarmManageService.queryData(userId);
        return list;
    }
//
    @ApiOperation(value = "导出历史告警事件列表")
    @RequiredPermission(enabled = false)
    @PostMapping(value = "/export/alarm-event-log")
    public Result exportAlarmEventLog(@RequestBody @ApiParam(name = "searchVO", value = "查询条件", required = true) TabSearchVO searchVO,
                                      HttpServletRequest request,
                                      HttpServletResponse response) throws Exception {
        TabSearchBO searchBO = BeanMapper.map(searchVO, TabSearchBO.class);
        searchBO.setNoActionGroupt(true);
        String startTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(searchVO.getStartTime()), ZoneOffset.of("+8")).format(ReportUtils.DF_YYYY_MM_DD_HH_SS);
        String endTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(searchVO.getEndTime()), ZoneOffset.of("+8")).format(ReportUtils.DF_YYYY_MM_DD_HH_SS);
        String name;
        if (Objects.equals(searchBO.getExportType(),REAL_TIME_ALARM_EXPORT_TYPE)){
            name = LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_REAL_TIME_ALARM.getMsg()) + System.currentTimeMillis() + CommonConstant.EXCELTYPE_XLS_X;
        }else if (Objects.equals(searchBO.getExportType(),REPAIR_ALARM_EXPORT_TYPE)){
            name = LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_REPAIR_ALARM.getMsg()) + System.currentTimeMillis() + CommonConstant.EXCELTYPE_XLS_X;
        }else {
            name = LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_HISTORICAL_ALARM.getMsg()) + startTime + Constants.SEPARATOR_MIDDLELINE + endTime + CommonConstant.EXCELTYPE_XLS_X;
        }
        String realName = name.replaceAll(":", "_");
        String filePath = File.separator + attachmentFilePath + File.separator + realName;
        //String filePath = "D:\\test\\" + System.currentTimeMillis() + ".xlsx";
        boolean flag = redisLockUtil.getLock(CommonCacheKeyConstant.CACHEKEY_REPORT_EXPORT_TIME, "yes", ReportUtils.EXPIRE_TIME);
        if (!flag) {
            return new Result(0, LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_EXECUTE.getMsg()), null);
        }
        try {
            Long userId = Long.valueOf(request.getHeader("User-ID"));
            Long projectId = Long.valueOf(request.getHeader("Projectid"));
            String finalName = name;
            Runnable runnable = new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    try {
                        LanguageUtil.setLanguage(request.getHeader(HttpRequestType.FB_LANGUAGE.getType()), request.getHeader(HttpRequestType.ACCEPT_LANGUAGE.getType()));
                        alarmExportService.exportAlarmEventlist(searchBO, filePath, realName, userId, projectId);
                    } catch (Exception e) {
                        log.error("导出"+finalName+"失败", e);
                        pushProgressInfo(0.0, userId, ImportRatioStatusDef.NO_START, null);
                    } finally {
                        redisLockUtil.releaseLock(CommonCacheKeyConstant.CACHEKEY_REPORT_EXPORT_TIME, "yes");
                    }
                }
            };
            executor.execute(runnable);
        } catch (Exception e) {
            log.error(e.getMessage());
            redisLockUtil.releaseLock(CommonCacheKeyConstant.CACHEKEY_REPORT_EXPORT_TIME, "yes");
        }
        ProcessInfoWithType processInfoWithType = new ProcessInfoWithType(EnumImportType.ALARM_EXPORT.getId(),
                new ProcessInfo(0.01, ImportRatioStatusDef.WORKING,
                        com.cet.futureblue.i18n.LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_EXECUTE.getMsg())),
                AlarmEventWebPushLogType.Alarm_Export.getId());
        return new Result(processInfoWithType);
    }

    private void pushProgressInfo(Double ratio, Long userId, Integer state, String filename) throws IOException {
        ProcessInfoWithType processInfoWithType = new ProcessInfoWithType(EnumImportType.ALARM_EXPORT.getId(),
                new ProcessInfoExtend(ratio, state, LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_EXECUTE.getMsg()), filename),
                AlarmEventWebPushLogType.Alarm_Export.getId());
        AlarmEventWebPushHandler.sendAlarmEventExportWebSocketMessage(processInfoWithType, userId);
    }

    @ApiOperation(value = "查询所有的告警类型")
    @GetMapping(value = "/query/all/alarm-types/{flag}")
    @RequiredPermission(enabled = false)
    public BaseVO queryAllAlarmTypes(@ApiParam(name = "flag", value = "中/英状态", required = true) @PathVariable("flag")Integer flag) {
        BaseVO baseVO = new BaseVO();
        List<AlarmTypeBO> list = alarmSettingService.queryAllAlarmTypes(flag);
        baseVO.setData(list);
        return baseVO;
    }

    @ApiOperation(value = "查询所有的告警类型及子类型")
    @GetMapping(value = "/query/all/pec-event-types")
    @RequiredPermission(enabled = false)
    public BaseVO queryAllPecEvenTypes() {
        BaseVO baseVO = new BaseVO();
        HttpServletRequest httpRequest = GlobalInfoUtils.getHttpRequest();
        LanguageUtil.setLanguage(httpRequest.getHeader(HttpRequestType.FB_LANGUAGE.getType()), httpRequest.getHeader(HttpRequestType.ACCEPT_LANGUAGE.getType()));
        List<AlarmTypeBO> list = alarmSettingService.queryAllPecEventTypes(false);
        baseVO.setData(list);
        return baseVO;
    }

    @ApiOperation(value = "查询所有的告警类型及子类型（包含平安事件）")
    @GetMapping(value = "/query/all/pec-event-types/include-safety")
    @RequiredPermission(enabled = false)
    public BaseVO queryAllIncludingSafety() {
        BaseVO baseVO = new BaseVO();
        List<AlarmTypeBO> list = alarmSettingService.queryAllPecEventTypes(true);
        baseVO.setData(list);
        return baseVO;
    }

    @ApiOperation(value = "查询告警等级")
    @RequiredPermission(enabled = false)
    @PostMapping(value = "/query/custom-alarm-level")
    public List queryCustomAlarmLevel(@RequestBody @ApiParam(name = "searchBO", value = "查询条件", required = true) CustomAlarmLevelVO customAlarmLevelVO) {
        CustomAlarmLevelBO customAlarmLevelBO = BeanMapper.map(customAlarmLevelVO, CustomAlarmLevelBO.class);
        List list = alarmSettingService.queryCustomAlarmLevelList(customAlarmLevelBO);
        return list;
    }

    @ApiOperation(value = "查询告警原因分类")
    @RequiredPermission(enabled = false)
    @PostMapping(value = "/query/alarm-cause-classify")
    public BaseVO queryAlarmCauseClassifyData(@RequestBody @ApiParam(name = "searchBO", value = "查询条件", required = true) AlarmCauseClassifyVO alarmCauseClassifyVO) {
        AlarmCauseClassifyBO alarmCauseClassifyBO = BeanMapper.map(alarmCauseClassifyVO, AlarmCauseClassifyBO.class);
        Result<List<AlarmCauseClassifyBO>> listResult = alarmSettingService.queryAlarmCauseClassifyList(alarmCauseClassifyBO);
        return new BaseVO(listResult.getData(), listResult.getTotal());
    }
    @ApiOperation(value = "实时事件确认接口")
    @RequiredPermission(enabled = false)
    @OperationLog(operationtype = 1, subtype = 6, description = "info.common.event.confirmation")
    @PutMapping(value = "/alarm-real-event-confirm", produces = "application/json")
    public Result<List> alarmRealEventConfirm(@RequestBody @ApiParam(name = "pecEventExtendConfirmBO", value = "修改信息VO", required = true) PecEventExtendConfirmVO pecEventExtendConfirmVO) {
        // TODO: 2024/8/27 修改逻辑，只改状态 
        PecEventExtendConfirmBO comfirmBO = BeanMapper.map(pecEventExtendConfirmVO, PecEventExtendConfirmBO.class);
        List list = alarmManageService.realAlarmEventConfirm(comfirmBO);
        return new Result<>(BaseRTC.SUCCESS, LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_PROCESS_SUCCESS.getMsg()), list);
    }
    @ApiOperation(value = "获取事件详情信息")
    @RequiredPermission(enabled = false)
    @GetMapping("/get/event-details")
    public BaseVO<PecAlarmExtendBO> getEventDetails(@RequestParam("id") @ApiParam(name = "id", value = "事件id", required = true) Long id) {
        BaseVO<PecAlarmExtendBO> baseVO = new BaseVO<>();
        PecAlarmExtendBO pecEventExtendBO = alarmManageService.getEventDetails(id);
        baseVO.setData(pecEventExtendBO);
        return baseVO;
    }

}
