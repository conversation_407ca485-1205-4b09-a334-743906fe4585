package com.cet.eem.alarmstrategyservice.controller;

/**
 * @describe: TODO
 * @author: z<PERSON><PERSON><PERSON>
 * @date: 2022/1/20 13:49
 */

import com.cet.datacenter.annotation.RequiredPermission;
import com.cet.eem.bll.alarmstrategy.exception.APIException;
import com.cet.datacenter.base.Result;
import com.cet.datacenter.bean.alarm.EditThresholdBO;
import com.cet.datacenter.bean.alarm.MeasureInRoomBO;
import com.cet.datacenter.bean.auth.UserBO;
import com.cet.datacenter.bean.config.SafetyEventConfigBO;
import com.cet.datacenter.bean.device.MeasuredbyBO;
import com.cet.datacenter.bean.devicedata.PecNodeBO;
import com.cet.datacenter.bean.model.access.Expressions;
import com.cet.datacenter.bean.model.access.Page;
import com.cet.datacenter.bzenum.language.AlarmServiceInterEnum;
import com.cet.datacenter.constant.BaseModelLabel;
import com.cet.datacenter.constant.BaseRTC;
import com.cet.datacenter.constant.Constants;
import com.cet.datacenter.constant.ModelOperateConstant;
import com.cet.datacenter.reference.ObjectPropertyUtil;
import com.cet.datacenter.service.api.IModelService;
import com.cet.datacenter.service.impl.pecnode.PecNodeServiceClient;
import com.cet.eem.bll.alarmstrategy.cache.GatewayCache;
import com.cet.eem.bll.alarmstrategy.cache.MemCache;
import com.cet.eem.bll.alarmstrategy.constant.AlarmCacheKeyConstant;
import com.cet.eem.bll.alarmstrategy.model.AlarmSubscriptionBO;
import com.cet.eem.bll.alarmstrategy.model.PecAlarmExtendBO;
import com.cet.eem.bll.alarmstrategy.model.single.ProtectedSwitch;
import com.cet.eem.bll.alarmstrategy.service.AlarmMatchService;
import com.cet.eem.bll.alarmstrategy.service.IReadLocalFileService;
import com.github.benmanes.caffeine.cache.Cache;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.roaringbitmap.longlong.Roaring64NavigableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisConnectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @Description: 告警配置-告警算法库页面相关接口
 * @Author: liuhaojian
 * @Date: Created in 10:55 2021/06/22
 */
@Api(value ="/datacenter/alarmer/v1/cache", tags = "缓存刷新")
@RequestMapping(value = "/datacenter/alarmer/v1/cache")
@RestController
@Slf4j
public class CacheController {
    @Autowired
    MemCache memCache;
    @Autowired
    GatewayCache gatewayCache;
//    @Autowired
//    SafetyStrategyPushCache safetyStrategyPushCache;
    @Autowired
    RedisTemplate redisTemplate;
    @Value("${protect-mode.limit}")
    private Integer eventLimit;

//    @Autowired
//    private Cache<Long, AlarmSubscriptionBO> alarmSubscriptionInfoCache;
    @Autowired
    PecNodeServiceClient client;
    @Autowired
    IReadLocalFileService readLocalFileService;
    @Autowired
    private AlarmMatchService alarmMatchService;

    @Autowired
    IModelService modelService;
//    @Autowired
//    SmsEventReadService iSmsEventReadService;
//    @Autowired
//    ISmsEventAlertService smsEventAlertService;
    /**
     * 刷新layerinfo
     * @return
     */
//    @ApiOperation(value = "修改告警短信开关")
//    @PostMapping("/update-alarm-email-switch")
//    @RequiredPermission(enabled = false)
//    public Result updateAlarmEmailSwitch(@RequestBody Boolean flag){
//        try {
//            if(flag!=null) {
//                smsEventAlertService.setSwitchFlag(flag);
//            }
//            return new Result();
//        } catch (Exception e) {
//            log.error("修改告警短信开关失败",e);
//            return new Result(AlarmServiceInterEnum.ERROR_ALARMSERVICE_MODIFY_SMS_SWITCH_FAILED,null);
//        }
//    }
    /**
     * 刷新layerinfo
     * @return
     */
//    @ApiOperation(value = "重置告警点")
//    @GetMapping("/reset-thresholdCache")
//    @RequiredPermission(enabled = false)
//    public Result refreshThresholdCache(){
//        try{
//           memCache.resetThresholdCache();
//            return new Result();
//        }catch (Exception ex){
//            log.error("重置告警点缓存失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//    /**
//     * 刷新layerinfo
//     * @return
//     */
//    @ApiOperation(value = "新增告警点")
//    @PostMapping("/edit-threshold-cache")
//    @RequiredPermission(enabled = false)
//    public Result editSwitchThresholdCache(@RequestBody EditThresholdBO ids){
//        try{
//            if(ids!=null&&Objects.equals(ids.getType(),1)){
//                memCache.editSwitchThresholdCache( ids);
//            }
//            if(ids!=null&&Objects.equals(ids.getType(),4)){
//                memCache.editEnumThresholdCache( ids);
//            }
//            return new Result();
//        }catch (Exception ex){
//            log.error("新增告警点缓存失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//    /**
//     * 刷新layerinfo
//     * @return
//     */
//    @ApiOperation(value = "刷新通信管理机缓存")
//    @PostMapping("/refresh-gateway-cache")
//    @RequiredPermission(enabled = false)
//    public Result refreshGatewayCache(){
//        try{
//            gatewayCache.resetCommunicateCache( );
//            return new Result();
//        }catch (Exception ex){
//            log.error("刷新通信管理机缓存失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//
//    @ApiOperation(value = "查询开关量告警点")
//    @GetMapping("/query-switch-thresholdCache/{type}/{nodeId}")
//    @RequiredPermission(enabled = false)
//    public Result querySwtichThresholdCache(@PathVariable @ApiParam(name = "type", value = "type", required = true) Integer type,
//                                            @PathVariable @ApiParam(name = "nodeId", value = "type", required = true) Long nodeId){
//        try{
//             Roaring64NavigableMap strategyCache = memCache.getSwtichThresCache(type);
//            if(strategyCache!=null) {
//              return new Result(strategyCache.contains(nodeId));
//          }
//            return new Result(false);
//        }catch (Exception ex){
//            log.error("查询告警点失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//    @ApiOperation(value = "查询枚举量告警点")
//    @GetMapping("/query-enum-thresholdCache/{type}/{nodeId}")
//    @RequiredPermission(enabled = false)
//    public Result queryEnumThresholdCache(@PathVariable @ApiParam(name = "type", value = "type", required = true) Integer type,
//                                          @PathVariable @ApiParam(name = "nodeId", value = "type", required = true) Long nodeId){
//        try{
//            Roaring64NavigableMap strategyCache = memCache.getenumThresCache(type);
//            if(strategyCache!=null) {
//                return new Result(strategyCache.contains(nodeId));
//            }
//            return new Result(false);
//        }catch (Exception ex){
//            log.error("查询告警点失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//    /**
//     * 刷新layerinfo
//     * @return
//     */
//    @ApiOperation(value = "刷新layerinfo")
//    @GetMapping("/refresh-layerinfo")
//    @RequiredPermission(enabled = false)
//    public Result refreshLayerInfo(){
//        try{
//            memCache.refreshLayerInfo();
//            return new Result();
//        }catch (Exception ex){
//            log.error("刷新layerinfo缓存失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//    /**
//     * 获取用户缓存
//     * @return
//     */
//    @ApiOperation(value = "获取用户缓存")
//    @GetMapping("/get-userInfo")
//    @RequiredPermission(enabled = false)
//    public Result getUserInfo(){
//        try{
//            List<UserBO> users = memCache.getUsers();
//            return new Result(users);
//        }catch (Exception ex){
//            log.error("获取用户缓存缓存失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//    /**
//     * 刷新layerinfo
//     * @return
//     */
//    @ApiOperation(value = "刷新peceventtype枚举")
//    @GetMapping("/refresh-pecevent-type")
//    @RequiredPermission(enabled = false)
//    public Result refreshPeceventType(){
//        try{
//            memCache.refreshPeceventType();
//            return new Result();
//        }catch (Exception ex){
//            log.error("刷新peceventtype枚举缓存失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//
//    @ApiOperation(value = "新增关联监测设备")
//    @PostMapping("/add-measure")
//    @RequiredPermission(enabled = false)
//    public Result addMeasureBy(@RequestBody List<MeasuredbyBO> addMeasuredbyList){
//        try{
//            memCache.addMeasureBy(addMeasuredbyList);
//            return new Result();
//        }catch (Exception ex){
//            log.error("新增关联监测设备",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//    @ApiOperation(value = "删除关联监测设备")
//    @PostMapping("/del-measure")
//    @RequiredPermission(enabled = false)
//    public Result delMeasureBy(@RequestBody List<Long> measuredbys){
//        try{
//            memCache.delMeasureBy(measuredbys);
//            return new Result();
//        }catch (Exception ex){
//            log.error("删除关联监测设备",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//    @ApiOperation(value = "修改房间名称")
//    @PostMapping("/update-roomName")
//    @RequiredPermission(enabled = false)
//    public Result updateRoomName(@RequestBody MeasureInRoomBO measuredbys){
//        try{
//            memCache.updateRoomName(measuredbys);
//            return new Result();
//        }catch (Exception ex){
//            log.error("删除关联监测设备",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//    @Autowired
//    IBroadcastService nameTypeService;
//    /**
//     * 刷新layerinfo
//     * @return
//     */
//    @ApiOperation(value = "刷新语音播报名称")
//    @GetMapping("/refresh-device-name")
//    @RequiredPermission(enabled = false)
//    public Result refreshNameMap(){
//        try{
//            nameTypeService.refreshNameMap();
//            return new Result();
//        }catch (Exception ex){
//            log.error("刷新BlockStrategy缓存失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//
//    /**
//     * 刷新layerinfo
//     * @return
//     */
//    @ApiOperation(value = "刷新双di点")
//    @GetMapping("/refresh-doubledi-cache")
//    @RequiredPermission(enabled = false)
//    public Result resetDoubleDi(){
//        try{
//            alarmMatchService.resetDoubleDI();
//            return new Result();
//        }catch (Exception ex){
//            log.error("刷新BlockStrategy缓存失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//    /**
//     * 刷新layerinfo
//     * @return
//     */
//    @ApiOperation(value = "刷新BlockStrategy")
//    @GetMapping("/refresh-block-strategy")
//    @RequiredPermission(enabled = false)
//    public Result refreshBlockStrategy(){
//        try{
//            memCache.refreshBlockStrategy();
//            return new Result();
//        }catch (Exception ex){
//            log.error("刷新BlockStrategy缓存失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//    /**
//     * 刷新layerinfo
//     * @return
//     */
//    @ApiOperation(value = "刷新userinfo")
//    @GetMapping("/refresh-userinfo")
//    @RequiredPermission(enabled = false)
//    public Result refreshUser(){
//        try{
//            memCache.refreshUser();
//            memCache.refreshUserMap();
//            return new Result();
//        }catch (Exception ex){
//            log.error("刷新userinfo缓存失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        }
//    }
//      /**
//     * 刷新layerinfo
//     * @return
//     */
//    @ApiOperation(value = "刷新actionCount")
//    @GetMapping("/refresh-actionCount")
//    @RequiredPermission(enabled = false)
//    public Result refreshActionCount(){
//        RedisConnectionFactory factory = redisTemplate.getConnectionFactory();
//        RedisConnection conn = null;
//        try{
//            conn = factory.getConnection();
//            Long aLong = conn.dbSize();
//            ActionMessageCount.getInstance().getCount().set(Integer.valueOf(aLong.toString()));
//            return new Result();
//        }catch (Exception ex){
//            log.error("刷新userinfo缓存失败",ex);
//            return new Result(-1,ex.getMessage(),null);
//        } finally {
//            RedisConnectionUtils.releaseConnection(conn, factory);
//        }
//    }

//    /**
//     * 重置保护模式开关
//     * @return
//     */
//    @ApiOperation(value = "resetReadLocalFilePoi")
//    @PostMapping("/resetReadLocalFilePoi")
//    @RequiredPermission(enabled = false)
//    public void resetReadLocalFilePoi(@RequestBody ResetPoiBO map){
//         Map<String, Long> cache = LocalFilePoiBO.INSTANCE.getCache(map.getUrl());
//        for (Map.Entry<String, Long> stringLongEntry : cache.entrySet()) {
//            if(map.getPoi().get(stringLongEntry.getKey())!=null){
//                cache.put(stringLongEntry.getKey(),map.getPoi().get(stringLongEntry.getKey()));
//            }
//        }
//    }
//    @ApiOperation(value = "getReadLocalFilePoi")
//    @PostMapping("/getReadLocalFilePoi")
//    @RequiredPermission(enabled = false)
//    public Map getReadLocalFilePoi(@RequestBody Map<String,Map<String,Long>> map){
//        Map<String, Map<String, Long>> cache = LocalFilePoiBO.INSTANCE.getCache();
//        return cache;
//    }
//
//    @ApiOperation(value = "获取告警短信订阅配置信息")
//    @PostMapping("/getAlarmSubcribeInfo")
//    @RequiredPermission(enabled = false)
//    public Map getAlarmSubcribeInfo() {
////        smsEventAlertService.loadAlarmSubscriptionInfo();
//        ConcurrentMap<Long, AlarmSubscriptionBO> map = alarmSubscriptionInfoCache.asMap();
//        return map;
//    }
//    @ApiOperation(value = "重置超过24小时未恢复告警缓存")
//    @PostMapping("/resetUnresumeAlarm")
//    @RequiredPermission(enabled = false)
//    public Result resetUnresumeAlarm() {
////        smsEventAlertService.loadAlarmSubscriptionInfo();
//        alarmMatchService.resetUnresumAlarm();
//        return new Result();
//    }
//
//    @ApiOperation(value = "同步场站")
//    @GetMapping("/syncronizeStation")
//    @RequiredPermission(enabled = false)
//    public Result syncronizeStation() {
//        try {
//            //查现场站信息
//            Result<List<PecNodeBO>> listResult = client.queryStaionNode();
//            if (Result.checkResultSuccessCode(listResult.getCode())) {
//                Map<String, Map<String, Long>> urlValues = LocalFilePoiBO.INSTANCE.getCache();
//                //获取现有的场站
//                Set<String> collect = listResult.getData().stream().map(node->node.getNodeId()+"").collect(Collectors.toSet());
//                if (MapUtils.isEmpty(urlValues)|| CollectionUtils.isEmpty(collect)) {
//                    return new Result();
//                } else {
//                    Set<String> nowStations = urlValues.values().stream().flatMap(node -> node.keySet().stream()).collect(Collectors.toSet());
//                    Set delResult = new HashSet<String>();
//                    delResult.addAll(nowStations);
//                    delResult.removeAll(collect);
//                    Set addResult = new HashSet<String>();
//                    addResult.addAll(collect);
//                    addResult.removeAll(nowStations);
//                    boolean flag = false;
//                    if (CollectionUtils.isNotEmpty(addResult) || CollectionUtils.isNotEmpty(delResult)) {
//                        flag = readLocalFileService.updateSta(addResult, delResult);
//                    }
//                    iSmsEventReadService.updateSta();
//                    if(CollectionUtils.isNotEmpty(delResult)&&flag){
//                        List<Expressions> exps = Stream.of(new Expressions("staid", ModelOperateConstant.IN, delResult)).collect(Collectors.toList());
//                        Result<List<LocalFileUrlBO>> modelInfosByLabel = modelService.findModelInfosByLabel(ModelLabelConstant.LocalFilePOI, exps, null, null, null, LocalFileUrlBO.class);
//                        if(Result.checkResultSuccessCode(modelInfosByLabel.getCode())&&CollectionUtils.isNotEmpty(modelInfosByLabel.getData())){
//                            List<Long> delIds = modelInfosByLabel.getData().stream().map(LocalFileUrlBO::getId).collect(Collectors.toList());
//                            modelService.delete(ModelLabelConstant.LocalFilePOI,delIds);
//                        }
//                    }
//                }
//            }
//        }catch (Exception ex){
//            log.error("检查场站失败",ex);
//            return new Result(AlarmServiceInterEnum.ERROR_ALARMSERVICE_SYNCHRONIZED_STATION_FAILED,null);
//        }
//        return new Result();
//    }
    /**
     * 重置保护模式开关
     * @return
     */
    @ApiOperation(value = "resetProtectMode")
    @GetMapping("/resetProtectMode")
    @RequiredPermission(enabled = false)
    public void getProperty(){
        try{
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTimeDate = LocalDateTime.of(LocalDate.now(), LocalTime.of(0, 0, 0));
            long startTime = startTimeDate.minusDays(startTimeDate.getDayOfMonth() - 1l).toInstant(ZoneOffset.of("+8")).toEpochMilli();
            List<Expressions> expressions = Stream.of(new Expressions(ObjectPropertyUtil.getColumnName(PecAlarmExtendBO::getEventTime), ModelOperateConstant.GE, startTime),
                    new Expressions(ObjectPropertyUtil.getColumnName(PecAlarmExtendBO::getEventTime), ModelOperateConstant.LE, endTime.toInstant(ZoneOffset.of("+8")).toEpochMilli())
            ).collect(Collectors.toList());
            Page page = new Page(0, 1);
            Result<List<PecAlarmExtendBO>> modelInfosByLabel = modelService.findModelInfosByLabel(BaseModelLabel.PEC_ALARM_EXTEND, expressions, null, page, null, PecAlarmExtendBO.class);
            int total = modelInfosByLabel.getTotal();
            ProtectedSwitch.INSTANCE.getEventNum().set(total);
            if(total>eventLimit){
                ProtectedSwitch.INSTANCE.getSwtich().set(true);
            }else {
                ProtectedSwitch.INSTANCE.getSwtich().set(false);
            }
            ProtectedSwitch.INSTANCE.setFlag(true);
            redisTemplate.opsForHash().put(AlarmCacheKeyConstant.CACHE_PROTECT_SWITCH, "num",ProtectedSwitch.INSTANCE.getEventNum());
            redisTemplate.opsForHash().put(AlarmCacheKeyConstant.CACHE_PROTECT_SWITCH, "key",ProtectedSwitch.INSTANCE.getSwtich());
        }catch (Exception ex){
            log.error("重置保护模式开关失败",ex);
            throw new APIException(ex.getMessage());
        }
    }

//    /**
//     * 重新读取alarm_subscribe_type
//     * @return
//     */
//    @ApiOperation(value = "resetAlarmSubscribeType")
//    @GetMapping("/resetAlarmSubscribeType")
//    @RequiredPermission(enabled = false)
//    public String resetAlarmSubscribeType(){
//        smsEventAlertService.setAlarmSubscribeType(null);
//        smsEventAlertService.setAlarmContentSource(null);
//        smsEventAlertService.setAlarmContentSubject(null);
//        String alarmSubscribeType = smsEventAlertService.getAlarmSubscribeType();
//        String alarmContentSource = smsEventAlertService.getAlarmContentSource();
//        String alarmContentSubject = smsEventAlertService.getAlarmContentSubject();
//        return alarmSubscribeType + Constants.SEPARATOR_UNDERLINE + alarmContentSource + Constants.SEPARATOR_UNDERLINE + alarmContentSubject;
//    }
//    /**
//     * 新增/更新平安策略推送缓存
//     *
//     * @return
//     */
//    @ApiOperation(value = "新增/更新平安策略推送缓存")
//    @PostMapping("/save-safetyPush-cache")
//    @RequiredPermission(enabled = false)
//    public Result saveSafetyPushCache(@RequestBody List<SafetyEventConfigBO> safetyEventConfigBOList) {
//        try {
//            safetyStrategyPushCache.saveSafetyStrategyPushCache(safetyEventConfigBOList);
//            return new Result();
//        } catch (Exception ex) {
//            log.error("新增/更新平安策略推送缓存失败", ex);
//            return new Result(-1, ex.getMessage(), null);
//        }
//    }
//
//    @ApiOperation(value = "loadSubscribeSendConfig")
//    @GetMapping("/loadSubscribeSendConfig")
//    @RequiredPermission(enabled = false)
//    public String loadSubscribeSendConfig(){
//        smsEventAlertService.initSendConfig();
//        String msg = "smsHourLimit: "+ AlarmSubscribeCache.smsHourLimit +
//                ",smsFrequencyS: "+ AlarmSubscribeCache.smsFrequencyS +
//                ",emailHourLimit: "+ AlarmSubscribeCache.emailHourLimit +
//                ",emailFrequencyS: "+ AlarmSubscribeCache.emailFrequencyS +
//                ",mipHourLimit: "+ AlarmSubscribeCache.voiceHourLimit +
//                ",mipFrequencyS: "+ AlarmSubscribeCache.voiceFrequencyS +
//                ",mideaMsgHourLimit: "+ AlarmSubscribeCache.mideaMsgHourLimit +
//                ",mideaMsgFrequencyS: "+ AlarmSubscribeCache.mideaMsgFrequencyS ;
//        return msg;
//    }
//    @ApiOperation(value = "删除平安策略推送缓存")
//    @PostMapping("/del-safetyPush-cache")
//    @RequiredPermission(enabled = false)
//    public Result delSafetyPushCache(@RequestBody List<Long> ids) {
//        try {
//            safetyStrategyPushCache.deleteSafetyStrategyPushCache(ids);
//            return new Result();
//        } catch (Exception ex) {
//            log.error("删除关联监测设备", ex);
//            return new Result(-1, ex.getMessage(), null);
//        }
//    }
//
//    @ApiOperation(value = "获取平安策略推送缓存")
//    @PostMapping("/getSafetyPushCache")
//    @RequiredPermission(enabled = false)
//    public Map getSafetyPushCache() {
//        return safetyStrategyPushCache.getSafetyStrategyPushCache();
//    }
//
//    @ApiOperation(value = "更新声光报警器设备id")
//    @GetMapping("/refreshAlarmLightDeviceId/{lightDeviceId}/{voiceDeviceId}")
//    @RequiredPermission(enabled = false)
//    public Result refreshAlarmLightDeviceId(@PathVariable @ApiParam(name = "lightDeviceId", value = "lightDeviceId") Long lightDeviceId,
//                                            @PathVariable @ApiParam(name = "voiceDeviceId", value = "voiceDeviceId") Long voiceDeviceId){
//        if(lightDeviceId != null && lightDeviceId > 0L){
//            memCache.setAlarmLightDeviceIdCache(CommonConstant.NAME_LIGHT, lightDeviceId);
//        }
//        if(voiceDeviceId != null && voiceDeviceId > 0L){
//            memCache.setAlarmLightDeviceIdCache(CommonConstant.NAME_VOICE, voiceDeviceId);
//        }
//        return new Result(BaseRTC.SUCCESS, null, memCache.getAlarmLightDeviceIdMap());
//    }

}
