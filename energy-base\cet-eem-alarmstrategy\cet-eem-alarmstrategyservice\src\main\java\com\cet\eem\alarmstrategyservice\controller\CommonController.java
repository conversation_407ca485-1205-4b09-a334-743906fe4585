package com.cet.eem.alarmstrategyservice.controller;

import com.cet.datacenter.annotation.OperationLog;
import com.cet.datacenter.annotation.RequiredPermission;
import com.cet.datacenter.base.*;
import com.cet.datacenter.bean.BaseTreeNodeBO;
import com.cet.datacenter.bean.auth.UserBO;
import com.cet.datacenter.bean.common.SearchBO;
import com.cet.datacenter.bean.device.CommonDeviceBO;
import com.cet.datacenter.bean.device.MeasuredbyBO;
import com.cet.datacenter.bean.devicedata.NodeBO;
import com.cet.datacenter.bean.model.AddFlatBO;
import com.cet.datacenter.bean.model.ModelEnumBO;
import com.cet.datacenter.bean.model.access.Expressions;
import com.cet.datacenter.bean.model.access.Order;
import com.cet.datacenter.bean.model.access.Page;
import com.cet.datacenter.constant.BaseModelLabel;
import com.cet.datacenter.constant.BaseRTC;
import com.cet.datacenter.constant.Constants;
import com.cet.datacenter.constant.ModelOperateConstant;
import com.cet.datacenter.service.api.IModelService;
import com.cet.datacenter.util.AttachmentUtils;
import com.cet.datacenter.util.BeanMapper;
import com.cet.eem.bll.alarmstrategy.constant.CommonConstant;
import com.cet.eem.bll.alarmstrategy.model.MeasurePointBO;
import com.cet.eem.bll.alarmstrategy.model.MultilayerCategory;
import com.cet.eem.bll.alarmstrategy.model.SpaceBaseNodeBO;
import com.cet.eem.bll.alarmstrategy.model.vo.ControlPointSearchVO;
import com.cet.eem.bll.alarmstrategy.model.vo.UnionDeviceVO;
import com.cet.eem.bll.alarmstrategy.service.IAlarmThresholdService;
import com.cet.eem.bll.alarmstrategy.service.ICommonService;
import com.cet.eem.bll.alarmstrategy.service.IDeviceDataService;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.internal.util.privilegedactions.GetResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description: 通用接口
 * @Author: liuhaojian
 * @Date: Created in 16:09 2020/12/17
 **/
@Api(value = "eem/v1/common", tags = "通用接口")
@RequestMapping(value = "eem/v1/common")
@RestController
@Slf4j
public class CommonController extends BaseController {
    @Autowired
    ICommonService commonService;
    @Autowired
    IModelService modelService;
    @Autowired
    IDeviceDataService deviceDataService;
    @Resource
    IAlarmThresholdService alarmThresholdService;
    @Autowired
    private MessageSource messageSource;

    @ApiOperation(value = "告警模糊搜索")
    @PostMapping(value = "/event-data/query")
    @RequiredPermission(enabled = false)
    public Result<List<SpaceBaseNodeBO>> getQueryEventDataWithKeyword(@RequestBody SearchBO keyword, HttpServletRequest request) {
        Result result = new Result();
        try {
            List<SpaceBaseNodeBO> data = commonService.getQueryEventDataWithKeyword(keyword.getKeyword(),keyword.getModelLabel());
            return new Result(data);
        } catch (APIException ex) {
            throw new APIException(ex.getCode(), "查询设备失败");
        }
    }
    @ApiOperation(value = "查询所有的统计量(支持测点名称关键字搜索)")
    @GetMapping(value = "/statistical/all")
    @RequiredPermission(enabled = false)
    public BaseVO queryAllStatistical(String keyword) {
        BaseVO baseVO = new BaseVO();
        List<MeasurePointBO> list = alarmThresholdService.queryAllStatisticalMeasurePoint(keyword);
        baseVO.setData(list);
        return baseVO;
    }

    @ApiOperation(value = "查询遥控点")
    @PostMapping(value = "/query/control-points")
    @RequiredPermission(enabled = false)
    public BaseVO queryControlPoints(
            @RequestBody @ApiParam(name = "controlPointSearchVO", value = "查询条件", required = true) ControlPointSearchVO controlPointSearchVO) {
        BaseVO baseVO = new BaseVO();
        List<UnionDeviceVO> controlPointBOs = deviceDataService.queryControlPoints(controlPointSearchVO.getDeviceIds(),
                controlPointSearchVO.getKeyword(), controlPointSearchVO.getControlType());
        baseVO.setData(controlPointBOs);
        return baseVO;
    }
    @ApiOperation(value = "子系统及对应设备类型")
    @GetMapping(value = "/query/sub-systemenum")
    @RequiredPermission(enabled = false)
    public BaseVO querySubsystemenum() {
        List<MultilayerCategory> modelEnumValueBOS = commonService.querySubsystemenum(false, null);
        return new BaseVO(modelEnumValueBOS);
    }

}
