package com.cet.eem.alarmstrategyservice.controller;

import com.cet.datacenter.annotation.OperationLog;
import com.cet.datacenter.annotation.RequiredPermission;
import com.cet.eem.bll.alarmstrategy.exception.APIException;
import com.cet.datacenter.base.BaseResultCode;
import com.cet.datacenter.base.BaseVO;
import com.cet.datacenter.base.Result;
import com.cet.datacenter.bzenum.language.AlarmInterEnum;
import com.cet.datacenter.bzenum.language.CommonInterEnum;
import com.cet.eem.bll.alarmstrategy.constant.TestPermissionConstant;
import com.cet.eem.bll.alarmstrategy.model.LinkNodeTreeBO;
import com.cet.eem.bll.alarmstrategy.model.vo.QueryDeviceVO;
import com.cet.eem.bll.alarmstrategy.service.IAlarmSubscriptionService;
import com.cet.futureblue.i18n.LanguageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @describe: TODO
 * @author: zhouzeyu
 * @date: 2020/12/31 14:40
 */
@Api(value = "eem/v1/alarm-subscription", tags = "告警订阅")
@RequestMapping(value = "eem/v1/alarmsubs")
@RestController
public class AlarmSubscriptionController {

    @Autowired
    IAlarmSubscriptionService alarmSubscriptionService;

    @ApiOperation(value = "按子系统查询设备")
    @RequiredPermission(value = TestPermissionConstant.ALARM_CONFIG,enabled = false)
    @PostMapping(value = "/devices/query")
    public List queryDevices(@RequestBody QueryDeviceVO vo) {
        try {
            List<LinkNodeTreeBO>nodeTree = alarmSubscriptionService.queryAllDevices(vo);
            return nodeTree;
        } catch (APIException ex) {
             throw new APIException (LanguageUtil.getMessage(CommonInterEnum.ERROR_COMMON_QUERY_DEVICE_FAIL.getMsg()));
        }
    }

}
