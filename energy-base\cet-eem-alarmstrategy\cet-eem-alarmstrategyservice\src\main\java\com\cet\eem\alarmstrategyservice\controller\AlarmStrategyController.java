package com.cet.eem.alarmstrategyservice.controller;

import com.cet.datacenter.annotation.OperationLog;
import com.cet.datacenter.annotation.RequiredPermission;
import com.cet.eem.bll.alarmstrategy.exception.APIException;
import com.cet.datacenter.base.BaseVO;
import com.cet.datacenter.base.Result;
import com.cet.datacenter.bzenum.EnumBlockType;
import com.cet.datacenter.bzenum.language.AlarmInterEnum;
import com.cet.datacenter.bzenum.language.CommonInterEnum;
import com.cet.eem.bll.alarmstrategy.constant.CommonConstant;
import com.cet.eem.bll.alarmstrategy.model.vo.*;
import com.cet.eem.bll.alarmstrategy.service.AlarmerServiceClient;
import com.cet.eem.bll.alarmstrategy.service.IAlarmRingService;
import com.cet.eem.bll.alarmstrategy.service.IAlarmStrategyService;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.common.i18n.CommonLangKeyDef;
import com.cet.futureblue.i18n.LanguageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * @Description: 告警配置-告警策略页面相关接口
 * @Author: HeQi
 * @Date: Created in 10:55 2021/1/5
 */
@Api(value = "eem/v1/alarm-strategy", tags = "告警策略接口")
@RequestMapping(value = "/eem/v1/alarm-strategy")
@RestController
@Slf4j
public class AlarmStrategyController {

    @Autowired
    IAlarmStrategyService iAlarmStrategyService;
    @Autowired
    AlarmerServiceClient client;

    @ApiOperation(value = "查询告警策略列表")
    @PostMapping(value = "/list")
    @RequiredPermission(enabled = false)
    public BaseVO<List<StrategySummaryVO>> queryStrategyList(@RequestBody @ApiParam(name = "listConditionVO", value = "查询条件", required = true) ListConditionVO listConditionVO) {
        BaseVO baseVO = new BaseVO();
        Result<List<StrategySummaryVO>> result = null;
        try {
            Long projectId = getProjectId();
            result = iAlarmStrategyService.queryStrategyList(listConditionVO.getKeyword(), listConditionVO.getStrategyType(), listConditionVO.getEnable(), listConditionVO.getPage(), listConditionVO.getEffectiveStatus(),listConditionVO.getShieldingType(), projectId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof APIException) {
                throw new APIException(((APIException) e).getCode(), e.getMessage());
            } else {
                throw new APIException(LanguageUtil.getMessage(AlarmInterEnum.INFO_ALARMSTRATEGYSERVICE_QUERY_STRATEGY_FAIL.getMsg()));
            }
        }
        baseVO.setData(result.getData());
        baseVO.setTotal(result.getTotal());
        return baseVO;
    }

    @ApiOperation(value = "批量删除告警策略")
    @PostMapping(value = "/delete")
    @OperationLog(operationtype = 1, subtype = 7, description = "info.common.log57")
    @RequiredPermission(enabled = false)
    public Result<Boolean> deleteStrategys(@RequestBody @ApiParam(name = "ids", value = "删除策略id集合", required = true) List<Long> ids) {
        Result<Boolean> result = new Result<>();
        try {
            result = iAlarmStrategyService.deleteByIds(ids);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if (e instanceof APIException) {
                throw new APIException(((APIException) e).getCode(), e.getMessage());
            } else {
                throw new APIException(LanguageUtil.getMessage(AlarmInterEnum.INFO_ALARMSTRATEGYSERVICE_DELETE_STRATEGY_FAIL.getMsg()));
            }
        }
        if (result.getData()) {
            try {
                client.refreshBlockStrategy();
            } catch (Exception e) {
                log.error("修改未处理状态失败", e);
            }
        }
        return result;
    }

    @ApiOperation(value = "保存屏蔽策略")
    @OperationLog(operationtype = 1, subtype = 7, description = "info.common.log58")
    @PostMapping(value = "/block-strategy/save")
    @RequiredPermission(enabled = false)
    public BaseVO<Boolean> saveBlockStrategy(@RequestBody @ApiParam(name = "blockStrategyVO", value = "屏蔽策略信息", required = true) @Valid BlockStrategyVO blockStrategyVO) {
        BaseVO baseVO = new BaseVO();
        try {
            Long projectId = getProjectId();
            Boolean isSuccess = iAlarmStrategyService.saveBlockStrategy(blockStrategyVO, projectId);
            baseVO.setData(isSuccess);
            if (!isSuccess) {
                throw new APIException(LanguageUtil.getMessage(AlarmInterEnum.INFO_ALARMSTRATEGYSERVICE_SAVE_SHIELDINGSTRATEGY_FAIL.getMsg()));
            }
            try {
                if(EnumBlockType.VOICE_BLOCK.getType().equals(blockStrategyVO.getStrategyLevel())
                        ||EnumBlockType.DEVICE_OVERHAUL.getType().equals(blockStrategyVO.getStrategyLevel())){
                    client.refreshBlockStrategy();
                }
            } catch (Exception e) {
                log.error("修改缓存状态失败", e);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            baseVO.setData(false);
            if (e instanceof APIException) {
                throw new APIException(((APIException) e).getCode(), e.getMessage());
            } else {
                throw new APIException(LanguageUtil.getMessage(AlarmInterEnum.ERROR_ALARMSTRATEGYSERVICE_SAVE_SHIELDINGSTRATEGY_EXCEPTION.getMsg()));
            }
        }
        return baseVO;
    }

    @ApiOperation(value = "查询屏蔽策略详情")
    @GetMapping(value = "/block-strategy/detail")
    @RequiredPermission(enabled = false)
    public BaseVO<BlockStrategyVO> queryBlockStrategy(@RequestParam @ApiParam(name = "id", value = "屏蔽策略id", required = true) Long id) {
        BaseVO baseVO = new BaseVO();
        baseVO.setData(iAlarmStrategyService.queryBlockStrategyById(id));
        return baseVO;
    }

    @ApiOperation(value = "保存防抖策略")
    @OperationLog(operationtype = 1, subtype = 7, description = "info.common.log59")
    @PostMapping(value = "/antishake-strategy/save")
    @RequiredPermission(enabled = false)
    public BaseVO<Boolean> saveAntishakeStrategy(@RequestBody @ApiParam(name = "antishakeStrategyVO", value = "防抖策略信息", required = true) @Valid AntishakeStrategyVO antishakeStrategyVO) {
        BaseVO baseVO = new BaseVO();
        try {
            Long projectId = getProjectId();
            Boolean isSuccess = iAlarmStrategyService.saveAntishakeStrategy(antishakeStrategyVO, projectId);
            baseVO.setData(isSuccess);
            if (!isSuccess) {
                throw new APIException(LanguageUtil.getMessage(AlarmInterEnum.INFO_ALARMSTRATEGYSERVICE_SAVE_ANTISHAKESTRATEGY_FAIL.getMsg()));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            baseVO.setData(false);
            if (e instanceof APIException) {
                throw new APIException(((APIException) e).getCode(), e.getMessage());
            } else {
                throw new APIException(LanguageUtil.getMessage(AlarmInterEnum.ERROR_ALARMSTRATEGYSERVICE_SAVE_ANTISHAKESTRATEGY_EXCEPTION.getMsg()));
            }
        }
        return baseVO;
    }

    @ApiOperation(value = "查询防抖策略详情")
    @GetMapping(value = "/antishake-strategy/detail")
    @RequiredPermission(enabled = false)
    public BaseVO<AntishakeStrategyVO> queryAntishakeStrategy(@RequestParam @ApiParam(name = "id", value = "防抖策略id", required = true) Long id) {
        BaseVO baseVO = new BaseVO();
        baseVO.setData(iAlarmStrategyService.queryAntishakeStrategyById(id));
        return baseVO;
    }

    @ApiOperation(value = "保存联动策略")
    @OperationLog(operationtype = 1, subtype = 7, description = "info.common.log60")
    @PostMapping(value = "/union-strategy/save")
    @RequiredPermission(enabled = false)
    public BaseVO<Boolean> saveUnionStrategy(@RequestBody @ApiParam(name = "unionStrategyVO", value = "联动策略信息", required = true) @Valid UnionStrategyVO unionStrategyVO) {
        BaseVO baseVO = new BaseVO();
        Long projectId = getProjectId();
        Boolean isSuccess = iAlarmStrategyService.saveUnionStrategy(unionStrategyVO, projectId);
        baseVO.setData(isSuccess);
        if (!isSuccess) {
            throw new APIException(LanguageUtil.getMessage(AlarmInterEnum.INFO_ALARMSTRATEGYSERVICE_SAVE_LINKAGESTRATEGY_FAIL.getMsg()));
        }
        return baseVO;
    }

    @ApiOperation(value = "查询联动策略详情")
    @GetMapping(value = "/union-strategy/detail")
    @RequiredPermission(enabled = false)
    public BaseVO<UnionStrategyVO> queryUnionStrategy(@RequestParam @ApiParam(name = "id", value = "联动策略id", required = true) Long id) {
        BaseVO baseVO = new BaseVO();
        baseVO.setData(iAlarmStrategyService.queryUnionStrategyById(id));
        return baseVO;
    }

    @ApiOperation(value = "修改告警策略启用状态")
    @OperationLog(operationtype = 1, subtype = 7, description = "info.common.log61")
    @GetMapping(value = "/enable-status/switch")
    @RequiredPermission(enabled = false)
    public BaseVO<Boolean> changeEnableStatus(@RequestParam @ApiParam(name = "id", value = "策略id", required = true) Long id,
                                              @RequestParam @ApiParam(name = "enableStatus", value = "启用状态", required = true) Boolean enableStatus) {
        BaseVO baseVO = new BaseVO();
        try {
            Boolean isSuccess = iAlarmStrategyService.changeEnableStatus(id, enableStatus);
            baseVO.setData(isSuccess);
            if (!isSuccess) {
                throw new APIException(LanguageUtil.getMessage(AlarmInterEnum.INFO_ALARMSTRATEGYSERVICE_SWITCH_POLICY_STATUS_FAIL.getMsg()));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            baseVO.setData(false);
            if (e instanceof APIException) {
                throw new APIException(((APIException) e).getCode(), e.getMessage());
            } else {
                throw new APIException(LanguageUtil.getMessage(AlarmInterEnum.ERROR_ALARMSTRATEGYSERVICE_SWITCH_POLICY_STATUS_EXCEPTION.getMsg()));
            }
        }
        return baseVO;
    }

    @ApiOperation(value = "批量新建屏蔽策略")
    @OperationLog(operationtype = 1, subtype = 7, description = "info.common.log62")
    @PostMapping(value = "/block-strategy/save/batch")
    @RequiredPermission(enabled = false)
    public BaseVO<Boolean> batchSaveBlockStrategys(@RequestBody @ApiParam(name = "batchBlockStrategyVO", value = "屏蔽策略信息", required = true)
                                                   @Valid BatchBlockStrategyVO batchBlockStrategyVO) {
        BaseVO baseVO = new BaseVO();
        try {
            Long projectId = getProjectId();
            Boolean isSuccess = iAlarmStrategyService.batchSaveBlockStrategys(batchBlockStrategyVO, projectId);
            baseVO.setData(isSuccess);
            if (!isSuccess) {
                throw new APIException(LanguageUtil.getMessage(AlarmInterEnum.INFO_ALARMSTRATEGYSERVICE_SAVE_SHIELDINGSTRATEGY_FAIL.getMsg()));
            }
            try {
                if(EnumBlockType.VOICE_BLOCK.getType().equals(batchBlockStrategyVO.getStrategyLevel())
                        ||EnumBlockType.DEVICE_OVERHAUL.getType().equals(batchBlockStrategyVO.getStrategyLevel())){
                    client.refreshBlockStrategy();
                }
            } catch (Exception e) {
                log.error("修改缓存状态失败", e);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            baseVO.setData(false);
            throw new APIException(LanguageUtil.getMessage(AlarmInterEnum.ERROR_ALARMSTRATEGYSERVICE_SAVE_SHIELDINGSTRATEGY_EXCEPTION.getMsg()));
        }
        return baseVO;
    }

    @ApiOperation(value = "pecservice规则反向生成告警策略")
    @OperationLog(operationtype = 1, subtype = 7, description = "info.common.log63")
    @PostMapping(value = "/rules2Strategy")
    @RequiredPermission(enabled = false)
    public BaseVO<Boolean> rules2Strategy(@RequestParam @ApiParam(name = "validKey", value = "执行代码", required = true) String validKey) {
        BaseVO baseVO = new BaseVO();
        Boolean result = false;
        if ("epmsv1.0".equals(validKey)) {
            result = iAlarmStrategyService.rules2Strategy();
        } else {
            baseVO.setMsg("validkey invalid");
        }
        baseVO.setData(result);
        return baseVO;
    }

    @ApiOperation(value = "同步")
    @OperationLog(operationtype = 1, subtype = 7, description = "info.common.log64")
    @GetMapping(value = "/synchronization")
    @RequiredPermission(enabled = false)
    public Result synchronization(@RequestParam @ApiParam(name = "id", value = "阈值策略id", required = true) Long id, @RequestParam @ApiParam(name = "type", value = "类型", required = true) Integer type,@RequestParam @ApiParam(name = "nodeType", value = "类型", required = true) Integer nodeType) {
        Result synchronization = iAlarmStrategyService.synchronization(id, type,nodeType);
        return synchronization;
    }

    @ApiOperation(value = "同步告警策略")
    @OperationLog(operationtype = 1, subtype = 7, description = "info.common.log65")
    @GetMapping(value = "/synchronization-strategy")
    @RequiredPermission(enabled = false)
    public Result synchronizationStrategy() {
        Long projectId = getProjectId();
        Lock syncLock = iAlarmStrategyService.getSyncLock();
        try {
            if (syncLock.tryLock(1, TimeUnit.SECONDS)) {
                try {
                  return   iAlarmStrategyService.synchronizationStrategy(projectId);
                } catch (Exception ex){
                    log.error("同步失败",ex);
                    return new Result(-1);
                }finally {
                    syncLock.unlock();
                }
            }else {
                return new Result(100, LanguageUtil.getMessage(AlarmInterEnum.INFO_ALARMSTRATEGYSERVICE_EXECUTING.getMsg()), null);
            }
        }catch (InterruptedException e){
            Thread.currentThread().interrupt();
            log.error("同步失败",e);
            throw new APIException(LanguageUtil.getMessage(CommonInterEnum.ERROR_COMMON_SYNCHRONIZATION_FAILED.getMsg()));
        }
    }

    /**
     * 获取项目id
     * @return
     */
    private Long getProjectId() {
        Long projectId = GlobalInfoUtils.getProjectId();
        if (Objects.isNull(projectId)) {
            throw new APIException(LanguageUtil.getMessage(CommonLangKeyDef.GlobalInfo.PROJECT_ID_NOT_NULL));
        }
        return projectId;
    }

}
