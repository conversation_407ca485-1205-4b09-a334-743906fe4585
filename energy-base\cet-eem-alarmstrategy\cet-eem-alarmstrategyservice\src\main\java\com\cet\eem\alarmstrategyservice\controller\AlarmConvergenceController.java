package com.cet.eem.alarmstrategyservice.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cet.datacenter.annotation.OperationLog;
import com.cet.datacenter.annotation.RequiredPermission;
import com.cet.eem.bll.alarmstrategy.exception.APIException;
import com.cet.datacenter.base.Result;
import com.cet.datacenter.bean.auth.UserBO;
import com.cet.datacenter.bean.notice.WebPushInfoBO;
import com.cet.datacenter.bean.notice.WebPushInfoDetailBO;
import com.cet.datacenter.bzenum.EnumWebPushLogType;
import com.cet.datacenter.bzenum.language.CommonInterEnum;
import com.cet.datacenter.reference.ObjectPropertyUtil;
import com.cet.datacenter.service.api.IUserManagerService;
import com.cet.datacenter.util.BeanMapper;
import com.cet.datacenter.util.ReportUtils;
import com.cet.eem.bll.alarmstrategy.constant.CommonCacheKeyConstant;
import com.cet.eem.bll.alarmstrategy.constant.CommonConstant;
import com.cet.eem.bll.alarmstrategy.model.AlarmConvEventBO;
import com.cet.eem.bll.alarmstrategy.model.ConvergenceEventBO;
import com.cet.eem.bll.alarmstrategy.model.TabSearchBO;
import com.cet.eem.bll.alarmstrategy.model.vo.TabSearchVO;
import com.cet.eem.bll.alarmstrategy.service.IAlarmConvergenceService;
import com.cet.eem.bll.alarmstrategy.service.INoticeService;
import com.cet.eem.bll.alarmstrategy.util.RedisLockUtil;
import com.cet.futureblue.i18n.LanguageUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executor;


/**
 * @ClassName: AlarmConvergenceController
 * @Description: 告警收敛分析接口
 * @Author: liuhaojian
 * @Date: 2021/04/29 14:20
 **/
@Api(value = "eem/v1/alarm-convergence", tags = "告警管理接口")
@RequestMapping(value = "eem/v1/alarm-convergence")
@RestController
@Slf4j
public class AlarmConvergenceController {

    @Autowired
    private IAlarmConvergenceService alarmConvergenceService;
    @Autowired
    private IUserManagerService userManagerService;
    @Autowired
    private INoticeService noticeService;

    @Resource(name = "commonExecutor")
    Executor executor;
    @Value("${folder.path}")
    private String attachmentFilePath;


    /**
     * 查询收敛事件列表
     * @param searchVO
     * @return
     */
    @ApiOperation(value = "收敛列表查询")
    @RequiredPermission(enabled = false)
    @PostMapping("/query/list")
    public Result<List<ConvergenceEventBO>> queryPageList(@RequestBody @ApiParam(name = "searchVO", value = "查询条件", required = true) TabSearchVO searchVO) {
        if (Objects.isNull(searchVO.getStartTime()) || Objects.isNull(searchVO.getEndTime())) {
            throw new APIException(LanguageUtil.getMessage(CommonInterEnum.ERROR_COMMON_PARAMETER_EMPTY.getMsg()));
        }
        TabSearchBO searchBO = BeanMapper.map(searchVO, TabSearchBO.class);
        Result<List<ConvergenceEventBO>> result = alarmConvergenceService.queryConvergenceEventList(searchBO, false);
        return result;
    }

    @ApiOperation(value = "收敛关联列表信息查询")
    @RequiredPermission(enabled = false)
    @PostMapping("/query/relation-list")
    public Result<List<AlarmConvEventBO>> queryRelationList(@RequestBody @ApiParam(name = "searchVO", value = "查询条件", required = true) TabSearchVO searchVO) {
        if (Objects.isNull(searchVO)) {
            throw new APIException(LanguageUtil.getMessage(CommonInterEnum.ERROR_COMMON_PARAMETER_EMPTY.getMsg()));
        }
        if (Objects.isNull(searchVO.getParentId()) || Objects.isNull(searchVO.getStartTime()) || Objects.isNull(searchVO.getEndTime())) {
            throw new APIException(LanguageUtil.getMessage(CommonInterEnum.ERROR_COMMON_PARAMETER_EMPTY.getMsg()));
        }
        TabSearchBO searchBO = BeanMapper.map(searchVO, TabSearchBO.class);
        Result<List<AlarmConvEventBO>> result = alarmConvergenceService.queryConvergenceRelationEventList(searchBO, false);
        return result;
    }


    // 默认通知类型
    public static final String FILENAME = "filename";
    public static final String USER = "user";
    public static final String FILEPATH = "filePath";
    public static final String CODE = "code";
    @Autowired
    RedisLockUtil redisLockUtil;

    @ApiOperation(value = "导出收敛事件列表")
    @RequiredPermission(enabled = false)
    @PostMapping("/export/list")
    public Result exportList(@RequestBody @ApiParam(name = "searchVO", value = "查询条件", required = true) TabSearchVO searchVO,
                             HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (Objects.isNull(searchVO)) {
            throw new APIException(LanguageUtil.getMessage(CommonInterEnum.ERROR_COMMON_PARAMETER_EMPTY.getMsg()));
        }
        TabSearchBO searchBO = BeanMapper.map(searchVO, TabSearchBO.class);
        Long userId;
        if (StringUtils.isBlank(request.getHeader("User-ID"))) {
            userId = 1L;
        } else {
            userId = Long.valueOf(request.getHeader("User-ID"));
        }
        //限制同一时刻只有一个账号可以导出
        boolean flag = redisLockUtil.getLock(CommonCacheKeyConstant.CACHEKEY_REPORT_EXPORT_TIME, "yes", ReportUtils.EXPIRE_TIME);
        if (!flag) {
            return new Result(-1, LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_EXECUTE.getMsg()), null);
        }
        try {
            Runnable runnable = new Runnable() {
                @Override
                public void run() {
                    String startTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(searchVO.getStartTime()), ZoneOffset.of("+8")).format(ReportUtils.DF_YYYY_MM_DD_HH_SS);
                    String endTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(searchVO.getEndTime()), ZoneOffset.of("+8")).format(ReportUtils.DF_YYYY_MM_DD_HH_SS);
                    String name = LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_CONVERGENCE_NAME.getMsg()) + startTime + "~" + endTime;
                    String excelName = name + ".xlsx";
                    String filePath = File.separator + attachmentFilePath + File.separator + excelName;
                    try {
                        //alarmConvergenceService.exportConvergenceEventList(searchBO, response, ExcelType.XLS_X);
                        alarmConvergenceService.exportConvergenceEventList(searchBO, filePath);
                        batchPushToWeb(userId, filePath, LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_SUCCESS.getMsg())
                                , LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_FILE_READY.getMsg()), excelName);
                    } catch (Exception e) {
                        if (e instanceof APIException) {
                            log.error("导出失败", e);
                        } else {
                            log.error(e.getMessage(), e);
                            batchPushToWeb(userId, null, LanguageUtil.getMessage(CommonInterEnum.ERROR_COMMON_EXPORT_FAILED.getMsg())
                                    , LanguageUtil.getMessage(CommonInterEnum.ERROR_COMMON_EXPORT_FILE_FAILED.getMsg()), excelName);
                        }
                    } finally {
                        redisLockUtil.releaseLock(CommonCacheKeyConstant.CACHEKEY_REPORT_EXPORT_TIME, "yes");
                    }
                }
            };
            executor.execute(runnable);
        } catch (Exception e) {
            log.error(e.getMessage());
            redisLockUtil.releaseLock(CommonCacheKeyConstant.CACHEKEY_REPORT_EXPORT_TIME, "yes");
        }
        return new Result(1000, LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_GENERATING.getMsg()), null);
    }

    @ApiOperation(value = "导出被收敛事件列表")
    @RequiredPermission(enabled = false)
    @PostMapping("/export/converged-list")
    public Result exportConvergedList(@RequestBody @ApiParam(name = "searchVO", value = "查询条件", required = true) TabSearchVO searchVO,
                                      HttpServletRequest request,HttpServletResponse response) throws Exception {
        if (Objects.isNull(searchVO)) {
            throw new APIException(LanguageUtil.getMessage(CommonInterEnum.ERROR_COMMON_PARAMETER_EMPTY.getMsg()));
        }
        TabSearchBO searchBO = BeanMapper.map(searchVO, TabSearchBO.class);
        Long userId;
        if (StringUtils.isBlank(request.getHeader("User-ID"))) {
            userId = 1L;
        } else {
            userId = Long.valueOf(request.getHeader("User-ID"));
        }
        //限制同一时刻只有一个账号可以导出
        boolean flag = redisLockUtil.getLock(CommonCacheKeyConstant.CACHEKEY_REPORT_EXPORT_TIME, "yes", ReportUtils.EXPIRE_TIME);
        if (!flag) {
            return new Result(-1, LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_EXECUTE.getMsg()), null);
        }
        try {
            Runnable runnable = new Runnable() {
                @Override
                public void run() {
                    String startTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(searchVO.getStartTime()), ZoneOffset.of("+8")).format(ReportUtils.DF_YYYY_MM_DD_HH_SS);
                    String endTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(searchVO.getEndTime()), ZoneOffset.of("+8")).format(ReportUtils.DF_YYYY_MM_DD_HH_SS);
                    String name = LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_CONVERGED_NAME.getMsg()) + startTime + "~" + endTime;
                    String excelName = name + ".xlsx";
                    String filePath = File.separator + attachmentFilePath + File.separator + excelName;
                    try {
                        alarmConvergenceService.exportConvergedEventList(searchBO, filePath);
                        batchPushToWeb(userId, filePath, LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_SUCCESS.getMsg())
                                , LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_FILE_READY.getMsg()), excelName);
                    } catch (Exception e) {
                        log.error("导出失败", e);
                        batchPushToWeb(userId, null, LanguageUtil.getMessage(CommonInterEnum.ERROR_COMMON_EXPORT_FAILED.getMsg())
                                , LanguageUtil.getMessage(CommonInterEnum.ERROR_COMMON_EXPORT_FILE_FAILED.getMsg()), excelName);
                    } finally {
                        redisLockUtil.releaseLock(CommonCacheKeyConstant.CACHEKEY_REPORT_EXPORT_TIME, "yes");
                    }
                }
            };
            executor.execute(runnable);
        } catch (Exception e) {
            log.error(e.getMessage());
            redisLockUtil.releaseLock(CommonCacheKeyConstant.CACHEKEY_REPORT_EXPORT_TIME, "yes");
        }
        return new Result(1000, LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_GENERATING.getMsg()), null);
    }

    private void batchPushToWeb(Long userId,String filePath,String isSuccess,String exportDetail,String excelName) {
        List<Long> tagIds = Lists.newArrayList(userId);
        UserBO userBO = userManagerService.queryUserBOByUserId(userId);
        List<WebPushInfoBO> pushInfoVOS = Lists.newArrayList();
        WebPushInfoBO webPushInfoVO = new WebPushInfoBO();
        webPushInfoVO.setSender(CommonConstant.COMMON_MESSAGE_TITLE);
        webPushInfoVO.setContent(isSuccess);
        JSONObject obj = new JSONObject();
        obj.put(ObjectPropertyUtil.getColumnName(WebPushInfoDetailBO::getNoticeType), EnumWebPushLogType.CUSTOM_EXPORT_TYPE.getLogType());
        obj.put(ObjectPropertyUtil.getColumnName(WebPushInfoDetailBO::getId), userId);
        obj.put(ObjectPropertyUtil.getColumnName(WebPushInfoDetailBO::getDetail), exportDetail);
        obj.put(FILENAME, excelName);
        obj.put(USER,userBO.getName());
        if (isSuccess.equals(LanguageUtil.getMessage(CommonInterEnum.INFO_COMMON_EXPORT_SUCCESS.getMsg()))){
            obj.put(FILEPATH,filePath);
            obj.put(CODE,0);
        }else {
            obj.put(CODE,-1);
        }
        webPushInfoVO.setDescription(JSON.toJSONString(obj));
        webPushInfoVO.setLogType(EnumWebPushLogType.CUSTOM_EXPORT_TYPE.getLogType());
        webPushInfoVO.setTags(tagIds);
        pushInfoVOS.add(webPushInfoVO);
        log.info("发送web消息的格式内容>>>" + JSON.toJSONString(pushInfoVOS));
        noticeService.sendToWeb(pushInfoVOS);
    }

}
