package com.cet.eem.bll.alarmstrategy.aspect;

import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.lang.annotation.*;

/**
 * @describe: TODO
 * @author: z<PERSON><PERSON><PERSON>
 * @date: 2022/12/23 10:10
 */
@Target({ElementType.FIELD,ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MeasureUnitWiteCellStyle {
    String fontName() default "Calibri";
    short fontHeightInPoints() default 12;
    HorizontalAlignment horizontalAlignment() default HorizontalAlignment.CENTER;
    VerticalAlignment verticalAlignment() default VerticalAlignment.CENTER;
    short dataFormat() default 0;
    boolean locked() default true;
}
