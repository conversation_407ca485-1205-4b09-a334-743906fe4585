package com.cet.eem.bll.alarmstrategy.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Date 2023-11-01 11:34
 * @Description: TODO
 * @Version 1.0
 */
@Target({ElementType.METHOD, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Repeatable(TranslateCronTasks.class)
public @interface TranslateCronTask {

    String cron() default "";

    String CRON_DISABLED = "-";
}
